from typing import Dict, Any
from swmmgo.modifier.measures.abstract_measure import AbstractMeasure

class AddRetentionChamber(AbstractMeasure):
    def __init__(self, config: Dict[str, Any]):
        self.config = config

    def apply_to_network(self, drainage_network):
        # Extract parameters from config
        storage_id = self.config['id']
        node_id = self.config['node_id']
        storage_depth = self.config.get('depth', 2.0)  # Default depth of 2.0 meters if not specified

        # Get the node to connect to
        node = drainage_network.get_node_by_id(node_id)

        # Get node coordinates and invert elevation
        node_coords = node["geometry"]["coordinates"]
        node_invert_elevation = node_coords[2]

        # Set storage invert elevation 0.05m above the node's invert elevation
        storage_invert_elevation = node_invert_elevation + 0.05 # TODO: Ask if should be larger height difference to ensure flow by gravity

        # Create storage coordinates 5 meters north of the node
        # North direction corresponds to increasing y-coordinate
        storage_coords = [node_coords[0], node_coords[1] + 0.01, storage_invert_elevation]

        # Create storage feature
        storage_feature = self.create_storage_feature(
            storage_id,
            storage_coords,
            {
                "max_depth": storage_depth,
                "surcharge_depth": self.config.get('surcharge_depth', 8.0),
                "area_function": {
                    "function_type": "FUNCTIONAL",
                    "coefficient": "0",
                    "exponent": 0.0,
                    "constant": 1000.0  # Initial value, will be updated in finalizeNetworkChanges
                }
            }
        )

        # Add storage to network
        drainage_network.add_nodes([storage_feature])

        # Create orifice link from storage to node
        link_id = f"Orifice_{storage_id}_to_{node_id}"
        storage_node = drainage_network.get_node_by_id(storage_id)

        # Initial orifice capacity - will be updated in finalizeNetworkChanges
        orifice_link = self.create_orifice_link(
            link_id,
            storage_node,
            node,
            {
                "orifice_type": "SIDE",
                "offset": 0.0,
                "discharge_coefficient": 0.65
            }
        )

        # Add link to network
        drainage_network.add_links([orifice_link])

        # Note: finalizeNetworkChanges will be called by GeojsonNetworkModifier after all measures are applied

    def create_orifice_link(self, id, start_node, end_node, custom_properties):
        """Create an orifice link between two nodes"""
        properties = {
            "pipe_ID": id,
            "pipe_type": "Orifice",
            "Inlet_Node_ID": start_node["id"],
            "Outlet_Node_ID": end_node["id"],
            "offset": custom_properties.get("offset", 0.0),
            "disch_coeff": custom_properties.get("discharge_coefficient", 0.65),  # Required for SWMM export 
            "orifice_type": custom_properties.get("orifice_type", "SIDE"),
            "type": custom_properties.get("orifice_type", "SIDE"),  # Required for SWMM export
            "flap_gate": custom_properties.get("flap_gate", "YES"),  # Required for SWMM export
            "open/close_time": custom_properties.get("open_close_time", 0.0),  # Required for SWMM export
            "xsection": {
                "Shape": "CIRCULAR",
                "Geom1": 0.1,  # Initial diameter, will be updated in finalizeNetworkChanges
                "Geom2": 0.0,
                "Geom3": 0,
                "Geom4": 0
            }
        }
        self.deep_update(properties, custom_properties)
        return self.create_link(id, start_node, end_node, properties, custom_properties)

    def finalizeNetworkChanges(self, drainage_network):
        # TODO: This is a temporary fix (that has a performance cost)
        drainage_network.createObjectConnections()
        
        # Extract parameters from config
        storage_id = self.config['id']
        node_id = self.config['node_id']
        storage_depth = self.config.get('depth', 2.0)
        specific_discharge = self.config.get('specific_discharge', 1.0)  # l/s/ha

        # Get the storage node
        storage_node = drainage_network.get_node_by_id(storage_id)

        # Calculate upstream effective area (ha)
        upstream_effective_area_ha = drainage_network.getUpstreamEffectiveArea(storage_node) # ha
        upstream_effective_area = upstream_effective_area_ha * 10000  # Convert ha to m²
        
        rain_depth = 0.032 # m
        rain_volume = upstream_effective_area * rain_depth
        volume = rain_volume  # Could consider duration here - Note from Emily: I dont think duration is relevant as the 32 mm are total rainfall in 10 year event

        # Update storage area constant to achieve the calculated volume
        storage_node['properties']['area_function']['constant'] = volume / storage_depth

        # Get the orifice link
        link_id = f"Orifice_{storage_id}_to_{node_id}"
        orifice_link = next((link for link in drainage_network.get_links()
                            if link["id"] == link_id), None)

        if orifice_link:
            # Calculate orifice diameter based on upstream area and specific discharge
            # specific_discharge is in l/s/ha, upstream_effective_area is in ha
            # Convert to m³/s for calculation
            discharge_m3s = (specific_discharge * upstream_effective_area_ha) / 1000

            # Calculate orifice diameter using orifice equation: Q = Cd * A * sqrt(2*g*h)
            # Assuming a design head of 1m and Cd of 0.65
            # A = Q / (Cd * sqrt(2*g*h))
            g = 9.81  # gravitational acceleration
            h = 1.0   # design head, m
            cd = float(orifice_link['properties']['disch_coeff'])

            area = discharge_m3s / (cd * (2 * g * h)**0.5)
            diameter = (4 * area / 3.14159)**0.5
            
            if diameter < 0.01:
                diameter = 0.01

            # Update orifice diameter
            orifice_link['properties']['xsection']['Geom1'] = str(diameter)
