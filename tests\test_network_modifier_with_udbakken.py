import os
import unittest
from swmmgo.exporters.geojson_to_swmm_converter import GeoJsonToSwmmConverter
from swmmgo.importer.geojson_network import GeojsonNetworkConnector, GeojsonNetworkDisconnector
from swmmgo.importer.swmm_to_geojson import SwmmToGeojsonConverter
from swmmgo.modifier.geojson_network_modifier import GeojsonNetworkModifier
from swmmgo.swmm_backend.swmm_backend import SwmmBackEnd
from swmmgo.utils.json_wrapper import JsonWrapper
from tests.data import TESTS_DATA_PATH, TESTS_OUTPUT_PATH
from tests.test_helper import TestHelper


class TestNetworkModifierWithUdbakken(unittest.TestCase):

    DEBUG = True  # Set to True to save modified drainage network for debugging

    def setUp(self):
        self.output_folder = os.path.join(TESTS_OUTPUT_PATH, 'network_modifier_udbakken')

        # Ensure output folder exists if debugging is enabled
        if self.DEBUG:
            os.makedirs(self.output_folder, exist_ok=True)

        # Load the udbakken drainage network
        helper = TestHelper()
        # network_file = helper.find_large_file('udbakken_drainage_network.json', 'novafos/ballerup/udbakken')
        # self.udbakken_network = JsonWrapper().load(network_file)
        status_swmm_file = helper.find_large_file('Udbakken - Egebjerg Sn_0.inp', 'novafos/ballerup/udbakken')
        self.udbakken_network = helper.getDrainageNetwork(status_swmm_file)

    def tearDown(self):
        self.udbakken_network = None

    def _debug_output(self, modified_network, output_name="modified_drainage_network"):
        """Helper method to handle debug output operations

        Args:
            modified_network: The modified network to save and process
            output_name: Base name for output files (default: "modified_drainage_network")
        """
        if not self.DEBUG:
            return

        # Disconnect the network
        disconnector = GeojsonNetworkDisconnector()
        disconnector.setConnectedGeojsonNetwork(modified_network)
        disconnector.disconnect()
        disconnected_geojson_network = disconnector.getDisconnectedGeojsonNetwork()

        output_file = os.path.join(self.output_folder, f"{output_name}.json")
        JsonWrapper().save(disconnected_geojson_network, output_file)
        print(f"Modified drainage network saved to {output_file}")

        swmm_file = os.path.join(self.output_folder, f"{output_name}.inp")
        converter = GeoJsonToSwmmConverter()
        converter.setGeojsonNetwork(disconnected_geojson_network)
        converter.setSwmmFile(swmm_file)
        converter.setTargetCRS('epsg:25832')
        converter.run()

        print(f"SWMM input file saved to {swmm_file}")

        # Run SWMM
        rain_files = [os.path.join(TESTS_DATA_PATH, 'swmm_backend_wrapper', 'cds_rains', 'rain_10.dat')]

        backend = SwmmBackEnd()
        backend.setSWMMFile(swmm_file)
        backend.setRainfall(rain_files)
        backend.setRainNumber(0)
        backend.setDestinationFolder(self.output_folder)
        backend.run()

    def test_udbakken_modify_subcatchment_imperviousness(self):
        """Test modifying subcatchment imperviousness"""
        
        measures = [{
            'measure_type': 'ModifySubcatchmentImperviousness',
            'parameters': {
                'from_value': 13.187430507078,
                'imperviousness': 8,
                'subcatchment_id': 'c_20151_9rx'
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_modify_subcatchment_imperviousness")

    def test_udbakken_modify_pipe_diameter(self):
        """Test modifying pipe diameter"""
        
        measures = [{
            'measure_type': 'ModifyPipeDiameter',
            'parameters': {
                'link_id': 'Link_979',
                'diameter': 1.1,
                'from_value': 1
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_modify_pipe_diameter")

    def test_udbakken_reroute_catchments_independent(self):
        """Test rerouting catchments to existing node"""
        
        measures = [{
            'measure_type': 'RerouteCatchments',
            'parameters': {
                'node_id': 'E78080R',
                'catchment_ids': [
                    'c_20151_9tq',
                    'c_20151_9at',
                    'c_20151_9ai',
                    'c_20151_9to',
                    'c_20151_9bv'
                ]
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_reroute_catchments_independent")

    def test_udbakken_remove_existing_features_1(self):
        """Test removing existing features (first instance)"""

        measures = [{
            'measure_type': 'RemoveExistingFeatures',
            'parameters': {
                'link_ids': ['01_R_103497'],
                'node_ids': ['E78085R'],
                'catchment_ids': []
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_remove_existing_features_1")

    def test_udbakken_modify_node(self):
        """Test modifying node inflow"""

        measures = [{
            'measure_type': 'ModifyNode',
            'parameters': {
                'inflow': 0.00112,
                'node_id': 'E78310R',
                'from_values': {'inflow': 0}
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_modify_node")

    def test_udbakken_remove_existing_features_2(self):
        """Test removing existing features (second instance)"""

        measures = [{
            'measure_type': 'RemoveExistingFeatures',
            'parameters': {
                'link_ids': [],
                'node_ids': [],
                'catchment_ids': ['c_20151_9ae']
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_remove_existing_features_2")

    def test_udbakken_add_lid_1(self):
        """Test adding LID (first instance)"""

        measures = [{
            'measure_type': 'AddLid',
            'parameters': {
                'area': 10,
                'number': 1,
                'lid_name': 'LID_c_20151_7000cm_28_IXQHQR',
                'lid_type': 'bio_retention_cell',
                'lid_center': [12.4893, 55.6867],
                'subcatchment_id': 'c_20151_7000cm_28',
                'polygon_coordinates': [
                    [12.489, 55.6866], [12.4896, 55.6866], [12.4896, 55.6868], [12.489, 55.6868]
                ]
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_add_lid_1")

    def test_udbakken_add_lid_2(self):
        """Test adding LID (second instance)"""

        measures = [{
            'measure_type': 'AddLid',
            'parameters': {
                'area': 10,
                'number': 1,
                'lid_name': 'LID_c_20151_9mp_IXQHQR',
                'lid_type': 'bio_retention_cell',
                'lid_center': [12.4893, 55.6867],
                'subcatchment_id': 'c_20151_9mp',
                'polygon_coordinates': [
                    [12.489, 55.6866], [12.4896, 55.6866], [12.4896, 55.6868], [12.489, 55.6868]
                ]
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_add_lid_2")

    def test_udbakken_add_offline_storage(self):
        """Test adding offline storage"""

        measures = [{
            'measure_type': 'AddOfflineStorage',
            'parameters': {
                'volume': 100,
                'node_id': 'E78440R',
                'pipe_link_id': 'OS_pipe_E78440R_KHSwtc',
                'pump_link_id': 'OS_pump_E78440R_KHSwtc',
                'weir_link_id': 'OS_weir_E78440R_KHSwtc',
                'storage_node_id': 'OS_storage_E78440R_KHSwtc',
                'pump_sump_node_id': 'OS_pump_sump_E78440R_KHSwtc',
                'weir_crest_height': 0.15
            }
        }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_add_offline_storage")

    def test_udbakken_stormwater_pond_group(self):
        """Test AddStormwaterPond with dependent RerouteCatchments, AddPumpPipeline, and AddGravityPipeline"""

        measures = [
            {
                'measure_type': 'AddStormwaterPond',
                'parameters': {
                    'pond_id': 'Pond_522664',
                    'pond_depth': 3,
                    'pond_center': [12.4893, 55.6867, 10],
                    'pond_volume': 2000,
                    'pond_polygon': [
                        [12.489, 55.6866], [12.4896, 55.6866], [12.4896, 55.6868], [12.489, 55.6868]
                    ]
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'Pond_522664',
                    'catchment_ids': [
                        'c_HER_14152_2at_1',
                        'c_HER_14152_2au_1',
                        'c_HER_14152_2av_1',
                        'c_HER_14152_2ax_1',
                        'c_HER_14152_2ay_1',
                        'c_HER_14152_2hm_1',
                        'c_HER_14152_2pc_1'
                    ]
                }
            },
            {
                'measure_type': 'AddPumpPipeline',
                'parameters': {
                    'capacity': 0.1,
                    'end_node': 'E78030R',
                    'start_node': 'Pond_522664',
                    'trajectory': [],
                    'pump_link_id': 'PumpPipeline_Pond_522664_E78030R_pump_IXQHQR',
                    'pipe_link_ids': [],
                    'trajectory_node_ids': []
                }
            },
            {
                'measure_type': 'AddGravityPipeline',
                'parameters': {
                    'slope': 2,
                    'diameter': 0.8,
                    'end_node': 'Pond_522664',
                    'pipe_ids': ['GPL_pipe_0_OSSPOG', 'GPL_pipe_1_OSSPOG', 'GPL_pipe_2_OSSPOG', 'GPL_pipe_3_OSSPOG', 'GPL_pipe_4_OSSPOG'],
                    'start_node': 'E78150R',
                    'coordinates': [
                        [12.4893, 55.6867, 10], [12.4894, 55.6868, 9.8], [12.4895, 55.6869, 9.6], [12.4896, 55.687, 9.4]
                    ],
                    'manhole_ids': ['GPL_0_OSSPOG', 'GPL_1_OSSPOG', 'GPL_2_OSSPOG', 'GPL_3_OSSPOG'],
                    'manhole_depths': [2, 2, 2, 2],
                    'manhole_elevations': [10, 9.8, 9.6, 9.4]
                }
            }
        ]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_stormwater_pond_group")

    def test_udbakken_add_catchment_with_reroute(self):
        """Test AddCatchment with dependent RerouteCatchments"""

        measures = [
            {
                'measure_type': 'AddCatchment',
                'parameters': {
                    'catchment_ID': 'Catchment_874582',
                    'imperviousness': 40,
                    'catchment_polygon': [
                        [12.489, 55.6866], [12.4896, 55.6866], [12.4896, 55.6868], [12.489, 55.6868]
                    ]
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'E78120R',
                    'catchment_ids': ['Catchment_874582']
                }
            }
        ]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_add_catchment_with_reroute")

    def test_udbakken_add_retention_chamber_with_reroute(self):
        """Test AddRetentionChamber with dependent RerouteCatchments"""

        measures = [
            {
                'measure_type': 'AddRetentionChamber',
                'parameters': {
                    'id': 'RC_6712001_hv9yss',
                    'depth': 2,
                    'node_id': '6712001',
                    'orifice_link_id': 'RC_orifice_6712001_hv9yss',
                    'specific_discharge': 10
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'RC_6712001_hv9yss',
                    'catchment_ids': [
                        'c_HER_14152_7000d_81',
                        'c_HER_14152_7000d_70',
                        'c_HER_14152_7000d_41',
                        'c_HER_14152_7000d_29'
                    ]
                }
            }
        ]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_add_retention_chamber_with_reroute")

    def test_udbakken_cascaded_retention_cells_group(self):
        """Test AddCascadedRetentionCells with multiple dependent RerouteCatchments and AddGravityPipeline"""

        measures = [
            {
                'measure_type': 'AddCascadedRetentionCells',
                'parameters': {
                    'width': 2.4,
                    'height': 1.8,
                    'coordinates': [
                        [12.4893, 55.6867, 10],
                        [12.4894, 55.6868, 9.8],
                        [12.4895, 55.6869, 9.6],
                        [12.4896, 55.687, 9.4]
                    ],
                    'orifice_ids': ['CRC_orifice_0_1rMZNp', 'CRC_orifice_1_1rMZNp', 'CRC_orifice_2_1rMZNp', 'CRC_orifice_3_1rMZNp'],
                    'storage_ids': ['CRC_0_1rMZNp', 'CRC_1_1rMZNp', 'CRC_2_1rMZNp', 'CRC_3_1rMZNp'],
                    'end_manhole_id': 'CRC_end_manhole_1rMZNp',
                    'specific_discharge': 10,
                    'end_manhole_coordinates': [12.4897, 55.6871, 9.2]
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'CRC_0_1rMZNp',
                    'catchment_ids': ['c_20151_7000cp_5']
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'CRC_1_1rMZNp',
                    'catchment_ids': ['c_20151_7000cm_16']
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'CRC_2_1rMZNp',
                    'catchment_ids': ['c_20151_7000cm_14']
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'CRC_3_1rMZNp',
                    'catchment_ids': ['c_20151_7000cm_15']
                }
            },
            {
                'measure_type': 'AddGravityPipeline',
                'parameters': {
                    'slope': 2,
                    'diameter': 0.8,
                    'end_node': 'E78365R',
                    'pipe_ids': ['GPL_pipe_0_RY8RW5', 'GPL_pipe_1_RY8RW5', 'GPL_pipe_2_RY8RW5', 'GPL_pipe_3_RY8RW5'],
                    'start_node': 'CRC_end_manhole_1rMZNp',
                    'coordinates': [
                        [12.4898, 55.6872, 9.0],
                        [12.4899, 55.6873, 8.8],
                        [12.49, 55.6874, 8.6]
                    ],
                    'manhole_ids': ['GPL_0_RY8RW5', 'GPL_1_RY8RW5', 'GPL_2_RY8RW5'],
                    'manhole_depths': [2, 2, 2],
                    'manhole_elevations': [9.0, 8.8, 8.6]
                }
            }
        ]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_cascaded_retention_cells_group")

    def test_udbakken_gravity_pipeline_with_catchment_group(self):
        """Test AddGravityPipeline and AddCatchment with dependent RerouteCatchments"""

        measures = [
            {
                'measure_type': 'AddGravityPipeline',
                'parameters': {
                    'slope': 2,
                    'diameter': 0.8,
                    'end_node': 'HER_6712023',
                    'pipe_ids': ['GPL_pipe_0_1QTPAR', 'GPL_pipe_1_1QTPAR', 'GPL_pipe_2_1QTPAR'],
                    'start_node': None,
                    'coordinates': [
                        [12.4893, 55.6867, 10],
                        [12.4894, 55.6868, 9.8],
                        [12.4895, 55.6869, 9.6]
                    ],
                    'manhole_ids': ['GPL_0_1QTPAR', 'GPL_1_1QTPAR', 'GPL_2_1QTPAR'],
                    'manhole_depths': [2, 2, 2],
                    'manhole_elevations': [10, 9.8, 9.6]
                }
            },
            {
                'measure_type': 'AddCatchment',
                'parameters': {
                    'catchment_ID': 'Catchment_613087',
                    'imperviousness': 40,
                    'catchment_polygon': [
                        [12.489, 55.6866], [12.4896, 55.6866], [12.4896, 55.6868], [12.489, 55.6868]
                    ]
                }
            },
            {
                'measure_type': 'RerouteCatchments',
                'parameters': {
                    'node_id': 'GPL_0_1QTPAR',
                    'catchment_ids': ['Catchment_613087']
                }
            }
        ]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_gravity_pipeline_with_catchment_group")

    def test_add_retention_chamber_no_reroute(self):
        """Test AddRetentionChamber without dependent RerouteCatchments: expecting area function constant to be 0"""

        measures = [{
                'measure_type': 'AddRetentionChamber',
                'parameters': {
                    'id': 'RC_6712001_hv9yss',
                    'depth': 2,
                    'node_id': '6712001',
                    'orifice_link_id': 'RC_orifice_6712001_hv9yss', #creates the id in the backend so either leave out or delete that bit in the code
                    'specific_discharge': 10
                }
            }]

        json_modifier = GeojsonNetworkModifier()
        json_modifier.setDrainageNetwork(self.udbakken_network)
        json_modifier.setMeasures(measures)
        json_modifier.apply()

        modified_network = json_modifier.getDrainageNetwork()
        self._debug_output(modified_network, "udbakken_add_retention_chamber_no_reroute")

if __name__ == "__main__":
    unittest.main()
